#!/bin/bash
# 致力于一键编译测试发布版本，当前测试部分还没开始
# 根据车型获取对应的 SDK_VERSION
function get_sdk_version() {
    local car_type=$1
    local sdk_version=""

    # 查找并提取对应车型的 SDK_VERSION 定义行
    sdk_version=$(grep -m 1 -A 1 "#if defined($car_type)" src/interface/tx_dms_sdk.cpp | grep "#define SDK_VERSION" | awk '{print $3}' | tr -d '"()')

    if [[ -z "$sdk_version" ]]; then
        echo "Error: Unable to find SDK_VERSION for car type $car_type"
        exit 1
    fi

    # 输出提取的版本号
    echo $sdk_version
}

function build_Linux_oax4600_aarch64() {
    local car_type=$1
    local sdk_version

    echo "Will Build 4600 realcar version"
    rm -rf build_Linux_oax4600-aarch64
    sdk_version=$(get_sdk_version $car_type)
    folder_name="${car_type}_${sdk_version}"
    mkdir -p "build_Linux_oax4600-aarch64/${folder_name}"  
    
    cd build_Linux_oax4600-aarch64
    cmake  -DCMAKE_TOOLCHAIN_FILE=../toolchains/linux_oax4600_aarch64.toolchain.cmake \
    -DCMAKE_BUILD_TYPE=Release  -DCOMPILER_NAME=Linux_aarch64 -DHTTP_INFERENCE=OFF  \
    -DCAR_BUILD_TYPE=${car_type} -DMODEL_TEST_MODE=OFF -DINTERNAL_TEST_TOOL_MODE=OFF -DMEM_ISSUE_DEBUG=OFF -DUSE_ACTIVATE=ON -DBYD_SOP=ON -DCMAKE_CXX_FLAGS=' -fPIC ' -DCMAKE_C_FLAGS=' -fPIC '  ..
    make -j20
    echo "Linux_oax4600-aarch64 编译完成"

    cp ./libtx_dms.so "$folder_name/"
    cp ../src/interface/tx_dms_sdk.h "$folder_name/"
    echo "将生成的文件放入文件夹: build_Linux_oax4600-aarch64/${folder_name}"

    zip -r "$folder_name.zip" "$folder_name"
    echo "File $folder_name.zip has been created and ready for download."

    cd ..
}

function build_Linux_x86_64() {
    local car_type=$1
    local sdk_version
    
    echo "Will Build x86_64 test version..."
    rm -rf build_linux_x86_64
    sdk_version=$(get_sdk_version $car_type)
    folder_name="${car_type}_${sdk_version}_x86"
    mkdir -p "build_linux_x86_64/${folder_name}"

    cd build_linux_x86_64
    cmake  -DCMAKE_BUILD_TYPE=Release -DLinux-x86_64=ON -DUSE_MNN=ON -DHTTP_INFERENCE=ON -DCOMPILER_NAME=Linux-x86_64 \
    -DMODEL_TEST_MODE=OFF -DINTERNAL_TEST_TOOL_MODE=OFF -DMEM_ISSUE_DEBUG=OFF -DUSE_ACTIVATE=ON -DBYD_SOP=OFF -DCAR_BUILD_TYPE=${car_type} ..
    make -j20
    echo "Linux_x86_64 编译完成"

    cp ./libtx_dms.so "$folder_name/"
    cp ./test_tongxing_dms_oax4600_http_image_tool "$folder_name/"
    cp ../resourc/oax4600/model "$folder_name/" -r
    echo "将生成的文件放入文件夹: build_linux_x86_64/${folder_name}"

    zip -r "$folder_name.zip" "$folder_name"
    echo "File $folder_name.zip has been created and ready for download."

    cd ..
}

car_type=$1  # 获取车型参数（如 BYD_SC3E）
build_selection=$2  # 获取编译类型参数（如 oax4600 或 x86_64）

if [[ -z "$car_type" ]]; then
    echo "Error: Please specify CAR_BUILD_TYPE (e.g., BYD_SC3E BYD_EQ BYD_EQ_R BYD_HA6 BYD_SC3E_R or BYD_SC3EFE BYD_SC3EFE_R)"
    exit 1
fi

if [[ -z "$build_selection" ]]; then
    # 执行编译操作(默认全部执行，可以根据需求只编译一个版本)
    # 输出4600实车版本
    build_Linux_oax4600_aarch64 $car_type &
    # 输出自动化测试版本
    build_Linux_x86_64 $car_type &
else
    if [[ "$build_selection" == "oax4600" ]]; then
        build_Linux_oax4600_aarch64 $car_type &
    elif [[ "$build_selection" == "x86_64" ]]; then
        build_Linux_x86_64 $car_type &
    fi
fi

# 等待所有后台进程完成
wait

# 待添加测试部分

echo "build_test_deploy.sh 脚本执行完成"
